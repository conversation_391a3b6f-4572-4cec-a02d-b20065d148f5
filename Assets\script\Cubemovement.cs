using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Cubemovement : MonoBehaviour
{
    public Transform LastPosition;
    private Vector3 fixedTargetPosition; // Store the target position to prevent runtime changes

    // Start is called before the first frame update
    void Start()
    {
        if (LastPosition != null)
        {
            // Store the target world position at start to prevent rotation with parent
            fixedTargetPosition = LastPosition.position;
            StartCoroutine(RotateAndMove());
        }
    }

    IEnumerator RotateAndMove()
    {
        // First rotate 90 degrees on Y-axis
        yield return StartCoroutine(RotateYAxis());

        // Then move to last position
        yield return StartCoroutine(MoveToLastPosition());
    }

    IEnumerator RotateYAxis()
    {
        Vector3 startRotation = transform.eulerAngles;
        Vector3 targetRotation = startRotation + new Vector3(0, 90, 0);
        float duration = 1f; // 1 second for rotation
        float elapsedTime = 0f;

        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / duration;

            // Use smooth interpolation
            t = Mathf.SmoothStep(0f, 1f, t);

            transform.eulerAngles = Vector3.Lerp(startRotation, targetRotation, t);
            yield return null;
        }

        // Ensure we end up exactly at the target rotation
        transform.eulerAngles = targetRotation;
    }

    IEnumerator MoveToLastPosition()
    {
        Vector3 startPosition = transform.localPosition;
        Vector3 targetPosition = fixedTargetPosition; // Use fixed local position instead of runtime position
        float duration = 2f;
        float elapsedTime = 0f;
        float stopDistance = 0.1f; // Distance threshold to stop movement

        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / duration;

            // Use smooth interpolation
            t = Mathf.SmoothStep(0f, 1f, t);

            Vector3 newPosition = Vector3.Lerp(startPosition, targetPosition, t);

            // Check if we're close enough to the target position
            float distanceToTarget = Vector3.Distance(newPosition, targetPosition);

            if (distanceToTarget <= stopDistance)
            {
                // Stop exactly at target local position
                transform.localPosition = targetPosition;
                break;
            }

            transform.localPosition = newPosition;
            yield return null;
        }

        // Ensure we end up exactly at the target position
        transform.position = targetPosition;
    }
}
